﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Entities.Concrete;
using Core.Utilities.Business;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class UserManager : IUserService
    {
        IUserDal _userDal;

        public UserManager(IUserDal userDal)
        {
            _userDal = userDal;
        }
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        //buraya üye olurken girildiğinden secured operation aspect koyma
        public IResult Add(User user)
        {
            _userDal.Add(user);
            return new SuccessResult(Messages.UserAdded);
        }
        [PerformanceAspect(3)]
        //buraya üye olurken girildiğinden secured operation aspect koyma
        public List<OperationClaim> GetClaims(User user)
        {
            return _userDal.GetClaims(user);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int id)
        {
            _userDal.Delete(id);
            return new SuccessResult(Messages.UserDeleted);
        }
        [SecuredOperation("owner")]
        [CacheAspect(1800)] // 30 dakika - Tüm kullanıcılar (Warm Data)
        public IDataResult<List<User>> GetAll()
        {
            return new SuccessDataResult<List<User>>(_userDal.GetAll());
        }
        //buraya login olurken veri çekildiği için secured operation aspect koyma
        public User GetByMail(string email)
        {
            return _userDal.Get(u => u.Email == email);
        }
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        ////[SecuredOperation("owner")]
        //[ValidationAspect(typeof(UserValidator))]
        public IResult Update(User user)
        {
            // Önce eski User bilgilerini al
            var oldUser = _userDal.Get(u => u.UserID == user.UserID);

            // User tablosunu güncelle
            _userDal.Update(user);

            // Eğer email, firstName veya lastName değişmişse CompanyUser tablosunu da güncelle
            // SOLID prensiplerine uygun: Complex database operations DAL katmanında
            if (oldUser != null &&
                (oldUser.Email != user.Email ||
                 oldUser.FirstName != user.FirstName ||
                 oldUser.LastName != user.LastName))
            {
                var syncResult = _userDal.SyncCompanyUserData(user, oldUser);
                if (!syncResult.Success)
                {
                    // CompanyUser sync hatası durumunda log'la ama ana işlemi bozma
                    System.Diagnostics.Debug.WriteLine($"CompanyUser sync warning: {syncResult.Message}");
                }
            }

            return new SuccessResult(Messages.UserUpdated);
        }

        //[SecuredOperation("owner,admin")]
        [CacheAspect(1800)] // 30 dakika - Kullanıcı detayı (Warm Data)
        public IDataResult<User> GetById(int userId)
        {
            // SOLID prensiplerine uygun: Validation logic'i DAL katmanına taşıdık
            return _userDal.GetUserByIdWithValidation(userId);
        }

        /// <summary>
        /// Member rolü olmayan kullanıcıları getirir (Lisans satın alma için)
        /// </summary>
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika - Member olmayan kullanıcılar (Warm Data)
        public IDataResult<List<User>> GetNonMembers()
        {
            try
            {
                var users = _userDal.GetNonMembers();
                return new SuccessDataResult<List<User>>(users, "Member olmayan kullanıcılar başarıyla getirildi.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<User>>("Kullanıcılar getirilirken bir hata oluştu: " + ex.Message);
            }
        }

        /// <summary>
        /// Member rolü olmayan kullanıcıları sayfalı olarak getirir (10K+ kullanıcı için)
        /// </summary>
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(900)] // 15 dakika - Sayfalanmış kullanıcılar (pagination)
        public IDataResult<List<User>> GetNonMembersPaginated(int page, int pageSize, string searchTerm)
        {
            try
            {
                // Sayfa ve boyut validasyonu
                if (page < 1) page = 1;
                if (pageSize < 1 || pageSize > 100) pageSize = 20; // Max 100 kayıt

                var users = _userDal.GetNonMembersPaginated(page, pageSize, searchTerm ?? "");
                return new SuccessDataResult<List<User>>(users, "Kullanıcılar başarıyla getirildi.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<List<User>>("Kullanıcılar getirilirken bir hata oluştu: " + ex.Message);
            }
        }

        /// <summary>
        /// Member rolü olmayan kullanıcı sayısını getirir
        /// </summary>
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)] // 10 dakika - Kullanıcı sayısı (Hot Data)
        public IDataResult<int> GetNonMembersCount(string searchTerm)
        {
            try
            {
                var count = _userDal.GetNonMembersCount(searchTerm ?? "");
                return new SuccessDataResult<int>(count, "Kullanıcı sayısı başarıyla getirildi.");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<int>("Kullanıcı sayısı getirilirken bir hata oluştu: " + ex.Message);
            }
        }



    }
}
