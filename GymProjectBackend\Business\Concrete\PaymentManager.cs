using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using Core.Utilities.Security.CompanyContext;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class PaymentManager : IPaymentService
    {
        IPaymentDal _paymentDal;
        private readonly ICompanyContext _companyContext;

        public PaymentManager(IPaymentDal paymentDal, ICompanyContext companyContext)
        {
            _paymentDal = paymentDal;
            _companyContext = companyContext;
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(900)] // 15 dakika - Sayfalanmış ödeme geçmişi (pagination)
        public IDataResult<PaginatedResult<PaymentHistoryDto>> GetPaymentHistoryPaginated(PaymentPagingParameters parameters)
        {
            var result = _paymentDal.GetPaymentHistoryPaginated(parameters);
            return new SuccessDataResult<PaginatedResult<PaymentHistoryDto>>(result);
        }
        [PerformanceAspect(3)]
        [SecuredOperation("owner,admin")]
        [CacheAspect(600)] // 10 dakika - Ödeme toplamları (Hot Data - Dashboard)
        public IDataResult<PaymentTotals> GetPaymentTotals(PaymentPagingParameters parameters)
        {
            try
            {
                var result = _paymentDal.GetPaymentTotals(parameters);
                return new SuccessDataResult<PaymentTotals>(result,
                    parameters.StartDate.HasValue || parameters.EndDate.HasValue
                        ? "Filtrelenmiş ödeme toplamları getirildi"
                        : "Mevcut ay ödeme toplamları getirildi");
            }
            catch (Exception ex)
            {
                return new ErrorDataResult<PaymentTotals>(null, "Ödeme toplamları hesaplanırken bir hata oluştu");
            }
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(PaymentValidator))]
        public IResult Add(Payment payment)
        {
            _paymentDal.Add(payment);
            return new SuccessResult(Messages.PaymentAdded);
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int id)
        {
            // SOLID prensiplerine uygun: Soft delete işlemini DAL katmanına devredildi
            return _paymentDal.SoftDeletePayment(id, _companyContext.GetCompanyId());
        }
        [SecuredOperation("owner,admin")]
        [CacheAspect(1800)] // 30 dakika - Tüm ödemeler (Warm Data)
        public IDataResult<List<Payment>> GetAll()
        {
            return new SuccessDataResult<List<Payment>>(_paymentDal.GetAll());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        [ValidationAspect(typeof(PaymentValidator))]
        public IResult Update(Payment payment)
        {
            // SOLID prensiplerine uygun: Complex business logic DAL katmanına devredildi
            return _paymentDal.UpdatePaymentWithBusinessLogic(payment, _companyContext.GetCompanyId());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika - Ödeme geçmişi (Warm Data)
        public IDataResult<List<PaymentHistoryDto>> GetPaymentHistory()
        {
            return new SuccessDataResult<List<PaymentHistoryDto>>(_paymentDal.GetPaymentHistory());
        }
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)] // 10 dakika - Borçlu üyeler (Hot Data - Dashboard)
        public IDataResult<List<PaymentHistoryDto>> GetDebtorMembers()
        {
            return new SuccessDataResult<List<PaymentHistoryDto>>(_paymentDal.GetDebtorMembers());
        }
        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult UpdatePaymentStatus(int paymentId, string paymentMethod)
        {
            // SOLID prensiplerine uygun: Payment status update logic DAL katmanına devredildi
            return _paymentDal.UpdatePaymentStatusWithBusinessLogic(paymentId, paymentMethod);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(3600)] // 1 saat - Aylık gelir trendi (Cold Data)
        public IDataResult<MonthlyRevenueDto> GetMonthlyRevenue(int year)
        {
            try
            {
                var result = _paymentDal.GetMonthlyRevenue(year);
                return new SuccessDataResult<MonthlyRevenueDto>(result, $"{year} yılı aylık gelir trendi başarıyla getirildi");
            }
            catch (Exception)
            {
                return new ErrorDataResult<MonthlyRevenueDto>("Aylık gelir trendi hesaplanırken bir hata oluştu");
            }
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(900)] // 15 dakika - Filtrelenmiş ödeme geçmişi (Warm Data)
        public IDataResult<List<PaymentHistoryDto>> GetAllPaymentHistoryFiltered(PaymentPagingParameters parameters)
        {
            // DAL katmanındaki yeni metodu çağırarak hem normal hem de borç ödemelerini
            // filtrelenmiş şekilde alıyoruz.
            var result = _paymentDal.GetAllCombinedPaymentHistory(parameters);
            return new SuccessDataResult<List<PaymentHistoryDto>>(result);
        }

    }
}
