using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Transaction;
using Core.Aspects.Autofac.Validation;
using Core.Utilities.Paging;
using Core.Utilities.Results;
using DataAccess.Abstract;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyExerciseManager : ICompanyExerciseService
    {
        ICompanyExerciseDal _companyExerciseDal;
        private readonly Core.Utilities.Security.CompanyContext.ICompanyContext _companyContext;

        public CompanyExerciseManager(ICompanyExerciseDal companyExerciseDal, 
                                     Core.Utilities.Security.CompanyContext.ICompanyContext companyContext)
        {
            _companyExerciseDal = companyExerciseDal;
            _companyContext = companyContext;
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika - Şirket egzersizleri (Warm Data)
        public IDataResult<List<CompanyExerciseDto>> GetCompanyExercises()
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExercises(companyId);
            return new SuccessDataResult<List<CompanyExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika - Kategoriye göre egzersizler (Warm Data)
        public IDataResult<List<CompanyExerciseDto>> GetCompanyExercisesByCategory(int categoryId)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExercisesByCategory(companyId, categoryId);
            return new SuccessDataResult<List<CompanyExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(900)] // 15 dakika - Filtrelenmiş egzersizler (pagination)
        public IDataResult<PaginatedResult<CompanyExerciseDto>> GetCompanyExercisesFiltered(CompanyExerciseFilterDto filter)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExercisesFiltered(companyId, filter);
            return new SuccessDataResult<PaginatedResult<CompanyExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(600)] // 10 dakika - Arama sonuçları (Hot Data)
        public IDataResult<List<CompanyExerciseDto>> SearchCompanyExercises(string searchTerm)
        {
            if (string.IsNullOrWhiteSpace(searchTerm))
            {
                return new ErrorDataResult<List<CompanyExerciseDto>>("Arama terimi boş olamaz.");
            }

            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.SearchCompanyExercises(companyId, searchTerm);
            return new SuccessDataResult<List<CompanyExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika - Egzersiz detayı (Warm Data)
        public IDataResult<CompanyExerciseDto> GetCompanyExerciseDetail(int exerciseId)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExerciseDetail(companyId, exerciseId);
            if (result == null)
            {
                return new ErrorDataResult<CompanyExerciseDto>("Salon egzersizi bulunamadı.");
            }

            return new SuccessDataResult<CompanyExerciseDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika - ID'ye göre egzersiz (Warm Data)
        public IDataResult<CompanyExerciseDto> GetById(int exerciseId)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCompanyExerciseDetail(companyId, exerciseId);
            if (result == null)
            {
                return new ErrorDataResult<CompanyExerciseDto>("Salon egzersizi bulunamadı.");
            }

            return new SuccessDataResult<CompanyExerciseDto>(result);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Add(CompanyExerciseAddDto exerciseAddDto)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Entity oluşturma ve mapping işlemini DAL katmanına taşıdık
            return _companyExerciseDal.AddCompanyExercise(exerciseAddDto, companyId);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Update(CompanyExerciseUpdateDto exerciseUpdateDto)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık güncelleme işlemini DAL katmanına taşıdık
            return _companyExerciseDal.UpdateCompanyExercise(companyId, exerciseUpdateDto);
        }

        [SecuredOperation("owner,admin")]
        [LogAspect]
        [TransactionScopeAspect]
        [PerformanceAspect(3)]
        public IResult Delete(int exerciseId)
        {
            var companyId = _companyContext.GetCompanyId();

            // SOLID prensiplerine uygun: Karmaşık soft delete işlemini DAL katmanına taşıdık
            return _companyExerciseDal.SoftDeleteCompanyExercise(exerciseId, companyId);
        }

        // Birleşik egzersiz listesi (System + Company)
        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika - Birleşik egzersiz listesi (Warm Data)
        public IDataResult<List<CombinedExerciseDto>> GetCombinedExercises()
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCombinedExercises(companyId);
            return new SuccessDataResult<List<CombinedExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(1800)] // 30 dakika - Kategoriye göre birleşik egzersizler (Warm Data)
        public IDataResult<List<CombinedExerciseDto>> GetCombinedExercisesByCategory(int categoryId)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCombinedExercisesByCategory(companyId, categoryId);
            return new SuccessDataResult<List<CombinedExerciseDto>>(result);
        }

        [SecuredOperation("owner,admin")]
        [PerformanceAspect(3)]
        [CacheAspect(900)] // 15 dakika - Filtrelenmiş birleşik egzersizler (pagination)
        public IDataResult<PaginatedResult<CombinedExerciseDto>> GetCombinedExercisesFiltered(SystemExerciseFilterDto filter)
        {
            var companyId = _companyContext.GetCompanyId();
            var result = _companyExerciseDal.GetCombinedExercisesFiltered(companyId, filter);
            return new SuccessDataResult<PaginatedResult<CombinedExerciseDto>>(result);
        }
    }
}
